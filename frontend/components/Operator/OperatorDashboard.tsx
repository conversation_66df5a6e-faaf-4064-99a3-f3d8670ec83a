'use client'
/* eslint-disable @typescript-eslint/no-explicit-any, react-hooks/exhaustive-deps */

import { useCallback, useRef, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Device } from 'mediasoup-client'

export function OperatorDashboard() {
  const [mediaSessionId, setMediaSessionId] = useState<string>('')
  const [connecting, setConnecting] = useState(false)
  const [connected, setConnected] = useState(false)
  const [started, setStarted] = useState(false)
  const [muted, setMuted] = useState(false)
  const [status, setStatus] = useState<string>('Idle')

  const wsRef = useRef<WebSocket | null>(null)
  const deviceRef = useRef<Device | null>(null)
  const sendTransportRef = useRef<any>(null)
  const recvTransportRef = useRef<any>(null)
  const producerRef = useRef<any>(null)
  const consumersRef = useRef<Map<string, any>>(new Map())
  const pendingRequestsRef = useRef<Map<number, { resolve: (v: any) => void; reject: (e: any) => void }>>(new Map())
  const nextRequestIdRef = useRef<number>(1)
  const remoteContainerRef = useRef<HTMLDivElement | null>(null)

  const wsBase = process.env.NEXT_PUBLIC_VOICE_ROUTER_WS_URL?.replace(/\/$/, '') || 'ws://localhost:8002'

  const log = useCallback((msg: string) => setStatus((s) => `${new Date().toLocaleTimeString()}: ${msg}\n` + s), [])

  const request = useCallback((method: string, data: any = {}) => {
    return new Promise<any>((resolve, reject) => {
      const id = nextRequestIdRef.current++
      pendingRequestsRef.current.set(id, { resolve, reject })
      wsRef.current?.send(JSON.stringify({ id, method, data }))
    })
  }, [])

  const handleWsMessage = useCallback((event: MessageEvent) => {
    const message = JSON.parse(event.data)
    if (message.type === 'response') {
      const p = pendingRequestsRef.current.get(message.id)
      if (p) {
        if (message.error) p.reject(new Error(message.error))
        else p.resolve(message.data)
        pendingRequestsRef.current.delete(message.id)
      }
    } else if (message.type === 'notification') {
      if (message.method === 'newProducer') {
        subscribe(message.data)
      } else if (message.method === 'producerClosed') {
        const consumer = Array.from(consumersRef.current.values()).find((c) => c.producerId === message.data.producerId)
        if (consumer) {
          consumersRef.current.delete(consumer.id)
          consumer.close()
        }
      }
    }
  }, [])

  const connectWS = useCallback(async (sessionId: string) => {
    return new Promise<void>((resolve, reject) => {
      try {
        const wsUrl = `${wsBase}?sessionId=${encodeURIComponent(sessionId)}`
        const ws = new WebSocket(wsUrl)
        wsRef.current = ws

        ws.onopen = async () => {
          log('WebSocket connected')
          resolve()
        }
        ws.onmessage = handleWsMessage
        ws.onerror = (e: any) => {
          log(`WS error: ${e?.message || e}`)
        }
        ws.onclose = () => {
          log('WebSocket closed')
          setConnected(false)
          setStarted(false)
        }
      } catch (e: any) {
        reject(e)
      }
    })
  }, [handleWsMessage, log, wsBase])

  const connectAndInitDevice = useCallback(async () => {
    log('Fetching router RTP capabilities...')
    const routerRtpCapabilities = (await request('getRouterRtpCapabilities')) as any

    log('Creating mediasoup device...')
    const device = new Device()
    await device.load({ routerRtpCapabilities })
    deviceRef.current = device

    await createRecvTransport()
    await createSendTransport()
    setConnected(true)

    // Try to list existing producers to subscribe if server supports it
    try {
      const list = await request('listProducers')
      if (Array.isArray(list)) {
        for (const { producerId } of list) {
          await subscribe({ producerId })
        }
      }
    } catch {
      // ignore if not supported
    }
  }, [log, request])

  async function createSendTransport() {
    log('Creating send transport...')
    const transportInfo = await request('createWebRtcTransport')
    const device = deviceRef.current!
    const sendTransport = device.createSendTransport(transportInfo)

    sendTransport.on('connect', async ({ dtlsParameters }: any, callback: any, errback: any) => {
      try {
        await request('connectWebRtcTransport', { transportId: sendTransport.id, dtlsParameters })
        callback()
        log('Send transport connected')
      } catch (e) {
        errback(e)
      }
    })

    sendTransport.on('produce', async ({ kind, rtpParameters }: any, callback: any, errback: any) => {
      try {
        const { id } = await request('produce', { transportId: sendTransport.id, kind, rtpParameters })
        callback({ id })
        log('Producing started')
      } catch (e) {
        errback(e)
      }
    })

    sendTransportRef.current = sendTransport
  }

  async function createRecvTransport() {
    log('Creating recv transport...')
    const transportInfo = await request('createWebRtcTransport')
    const device = deviceRef.current!
    const recvTransport = device.createRecvTransport(transportInfo)

    recvTransport.on('connect', async ({ dtlsParameters }: any, callback: any, errback: any) => {
      try {
        await request('connectWebRtcTransport', { transportId: recvTransport.id, dtlsParameters })
        callback()
        log('Recv transport connected')
      } catch (e) {
        errback(e)
      }
    })

    recvTransportRef.current = recvTransport
  }

  async function subscribe({ producerId }: { producerId: string }) {
    try {
      const device = deviceRef.current!
      const recvTransport = recvTransportRef.current
      const consumerInfo = await request('consume', { transportId: recvTransport.id, producerId, rtpCapabilities: device.rtpCapabilities })
      const consumer = await recvTransport.consume(consumerInfo)
      consumersRef.current.set(consumer.id, consumer)

      await request('resumeConsumer', { consumerId: consumer.id })

      const { track } = consumer
      const audioEl = document.createElement('audio')
      audioEl.srcObject = new MediaStream([track])
      audioEl.autoplay = true
      audioEl.controls = true
      remoteContainerRef.current?.appendChild(audioEl)
      log(`Subscribed to producer ${producerId}`
      )
    } catch (e: any) {
      log(`Error subscribing: ${e.message || e}`)
    }
  }

  const startAudio = useCallback(async () => {
    if (!sendTransportRef.current) return
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    const track = stream.getAudioTracks()[0]
    const producer = await sendTransportRef.current.produce({ track })
    producerRef.current = producer
    setStarted(true)
    setMuted(false)
    log('Mic started')
  }, [log])

  const toggleMute = useCallback(async () => {
    const producer = producerRef.current
    if (!producer) return
    if (muted) {
      await producer.resume()
      await request('resumeProducer', { producerId: producer.id })
      setMuted(false)
      log('Unmuted')
    } else {
      await producer.pause()
      await request('pauseProducer', { producerId: producer.id })
      setMuted(true)
      log('Muted')
    }
  }, [muted, request, log])

  const connectToSession = useCallback(async () => {
    if (!mediaSessionId) return
    try {
      setConnecting(true)
      await connectWS(mediaSessionId)
      await connectAndInitDevice()
      log('Connected and ready')
    } catch (e: any) {
      log(`Failed to connect: ${e.message || e}`)
    } finally {
      setConnecting(false)
    }
  }, [connectAndInitDevice, connectWS, log, mediaSessionId])

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Operator</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div className="space-y-2">
              <Label>Media Session ID</Label>
              <Input value={mediaSessionId} onChange={(e) => setMediaSessionId(e.target.value)} placeholder="Paste mediaSessionId" />
            </div>
            <div className="flex gap-2">
              <Button onClick={connectToSession} disabled={!mediaSessionId || connecting || connected}>Connect</Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={startAudio} disabled={!connected || started}>Start Mic</Button>
            <Button onClick={toggleMute} variant="secondary" disabled={!started}>{muted ? 'Unmute' : 'Mute'}</Button>
          </div>

          <div>
            <Label>Status</Label>
            <pre className="bg-gray-100 p-3 rounded text-xs max-h-48 overflow-auto whitespace-pre-wrap">{status}</pre>
          </div>

          <div>
            <Label>Remote Audio</Label>
            <div ref={remoteContainerRef} className="space-y-2" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
