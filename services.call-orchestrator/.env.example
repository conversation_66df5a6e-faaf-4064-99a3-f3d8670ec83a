# Service Configuration
SERVICE_NAME=call-orchestrator
DEBUG=false
HOST=0.0.0.0
PORT=8002

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/cortexa

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
# Silence KafkaJS partitioner warning (we're using legacy partitioner explicitly)
KAF<PERSON>JS_NO_PARTITIONER_WARNING=1

# Internal Service URLs
VOICE_ROUTER_URL=http://localhost:3001

# Environment
NODE_ENV=development
