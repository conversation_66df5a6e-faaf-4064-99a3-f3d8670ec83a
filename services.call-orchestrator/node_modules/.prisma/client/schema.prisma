// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum CallStatus {
  QUEUED
  ACTIVE
  ENDED
}

model Call {
  id                String     @id @default(uuid()) @db.Uuid
  status            CallStatus @default(QUEUED)
  createdAt         DateTime   @default(now()) @map("created_at")
  endedAt           DateTime?  @map("ended_at")
  mediaSessionId    String?    @unique @map("media_session_id")
  translationForkId String?    @map("translation_fork_id")

  @@map("calls")
}
