{"version": 3, "names": ["_validate", "require", "_index", "validateNode", "node", "fields", "NODE_FIELDS", "type", "keys", "BUILDER_KEYS", "key", "field", "validateInternal"], "sources": ["../../src/builders/validateNode.ts"], "sourcesContent": ["import { validateInternal } from \"../validators/validate.ts\";\nimport type * as t from \"../index.ts\";\nimport { BUILDER_KEYS, NODE_FIELDS } from \"../index.ts\";\n\nexport default function validateNode<N extends t.Node>(node: N) {\n  if (node == null || typeof node !== \"object\") return;\n  const fields = NODE_FIELDS[node.type];\n  if (!fields) return;\n\n  // todo: because keys not in BUILDER_KEYS are not validated - this actually allows invalid nodes in some cases\n  const keys = BUILDER_KEYS[node.type] as (keyof N & string)[];\n  for (const key of keys) {\n    const field = fields[key];\n    if (field != null) validateInternal(field, node, key, node[key]);\n  }\n  return node;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAEe,SAASE,YAAYA,CAAmBC,IAAO,EAAE;EAC9D,IAAIA,IAAI,IAAI,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;EAC9C,MAAMC,MAAM,GAAGC,kBAAW,CAACF,IAAI,CAACG,IAAI,CAAC;EACrC,IAAI,CAACF,MAAM,EAAE;EAGb,MAAMG,IAAI,GAAGC,mBAAY,CAACL,IAAI,CAACG,IAAI,CAAyB;EAC5D,KAAK,MAAMG,GAAG,IAAIF,IAAI,EAAE;IACtB,MAAMG,KAAK,GAAGN,MAAM,CAACK,GAAG,CAAC;IACzB,IAAIC,KAAK,IAAI,IAAI,EAAE,IAAAC,0BAAgB,EAACD,KAAK,EAAEP,IAAI,EAAEM,GAAG,EAAEN,IAAI,CAACM,GAAG,CAAC,CAAC;EAClE;EACA,OAAON,IAAI;AACb", "ignoreList": []}