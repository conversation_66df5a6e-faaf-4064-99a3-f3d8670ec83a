{"name": "@jest/diff-sequences", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/diff-sequences"}, "license": "MIT", "description": "Compare items in two sequences to find a longest common subsequence", "keywords": ["fast", "linear", "space", "callback", "diff"], "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "devDependencies": {"@fast-check/jest": "^2.1.1", "benchmark": "^2.1.4", "diff": "^7.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7"}